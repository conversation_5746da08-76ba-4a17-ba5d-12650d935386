{% extends 'base.html' %}

{% block title %}Mark Attendance - Smart Management System{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-top: 2rem;
    }
    
    .form-header {
        background: var(--gradient-bg);
        color: white;
        padding: 1.5rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .quick-actions {
        background: #f8fafc;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .quick-action-btn {
        background: white;
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
        padding: 1rem 1.5rem;
        border-radius: 15px;
        text-decoration: none;
        display: block;
        text-align: center;
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }
    
    .quick-action-btn:hover {
        background: var(--primary-color);
        color: white;
        transform: translateY(-2px);
    }
    
    .current-time {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .time-display {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .date-display {
        font-size: 1.2rem;
        opacity: 0.9;
    }
    
    .status-info {
        background: #eff6ff;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid var(--primary-color);
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-clock me-3"></i>Mark Attendance
                        </h1>
                        <p class="lead mb-0">Record your daily attendance</p>
                    </div>
                    <div>
                        <a href="{% url 'core:attendance_list' %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Attendance
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                
                <!-- Current Time Display -->
                <div class="current-time">
                    <div class="time-display" id="currentTime">--:--:--</div>
                    <div class="date-display" id="currentDate">Loading...</div>
                </div>
                
                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h5 class="mb-3"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <a href="#" class="quick-action-btn" onclick="quickCheckIn()">
                                <i class="fas fa-sign-in-alt fa-2x mb-2"></i>
                                <div><strong>Check In</strong></div>
                                <small>Mark your arrival</small>
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="#" class="quick-action-btn" onclick="quickCheckOut()">
                                <i class="fas fa-sign-out-alt fa-2x mb-2"></i>
                                <div><strong>Check Out</strong></div>
                                <small>Mark your departure</small>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Today's Status -->
                <div class="status-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Today's Status</h6>
                    <p class="mb-1"><strong>Status:</strong> <span class="badge bg-success">Present</span></p>
                    <p class="mb-1"><strong>Check-in:</strong> 09:15 AM</p>
                    <p class="mb-1"><strong>Working Hours:</strong> <span id="workingHours">0.0</span> hours</p>
                    <p class="mb-0"><strong>Location:</strong> Office</p>
                </div>
                
                <!-- Manual Attendance Form -->
                <form method="post" id="attendanceForm">
                    {% csrf_token %}
                    
                    <h5 class="mb-3"><i class="fas fa-edit me-2"></i>Manual Entry</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Date <span class="text-danger">*</span></label>
                                <input type="date" name="date" class="form-control" value="{{ today }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Status <span class="text-danger">*</span></label>
                                <select name="status" class="form-control" required>
                                    <option value="">Select Status</option>
                                    <option value="Present">Present</option>
                                    <option value="Absent">Absent</option>
                                    <option value="Leave">Leave</option>
                                    <option value="Half-Day">Half-Day</option>
                                    <option value="WFH">Work From Home</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Check-in Time</label>
                                <input type="time" name="check_in" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Check-out Time</label>
                                <input type="time" name="check_out" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Break Time (hours)</label>
                                <input type="number" name="break_time" class="form-control" step="0.25" min="0" placeholder="e.g., 1.0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Location</label>
                                <input type="text" name="location" class="form-control" placeholder="Office, Home, Client site, etc.">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Remarks</label>
                        <textarea name="remarks" class="form-control" rows="3" placeholder="Any additional notes..."></textarea>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-save me-2"></i>Save Attendance
                        </button>
                        <a href="{% url 'core:attendance_list' %}" class="btn btn-secondary btn-lg">
                            <i class="fas fa-list me-2"></i>View Records
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update current time
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        const dateString = now.toLocaleDateString('en-US', { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        });
        
        document.getElementById('currentTime').textContent = timeString;
        document.getElementById('currentDate').textContent = dateString;
        
        // Update working hours if checked in
        updateWorkingHours();
    }
    
    function updateWorkingHours() {
        // This would calculate working hours based on check-in time
        // For demo purposes, showing a sample calculation
        const checkInTime = '09:15';
        if (checkInTime) {
            const now = new Date();
            const checkIn = new Date();
            const [hours, minutes] = checkInTime.split(':');
            checkIn.setHours(parseInt(hours), parseInt(minutes), 0, 0);
            
            const diffMs = now - checkIn;
            const diffHours = diffMs / (1000 * 60 * 60);
            
            if (diffHours > 0) {
                document.getElementById('workingHours').textContent = diffHours.toFixed(1);
            }
        }
    }
    
    // Update time every second
    updateTime();
    setInterval(updateTime, 1000);
    
    // Quick check-in function
    window.quickCheckIn = function() {
        const now = new Date();
        const timeString = now.toTimeString().slice(0, 5);
        
        // Fill form with current time
        document.querySelector('input[name="check_in"]').value = timeString;
        document.querySelector('select[name="status"]').value = 'Present';
        document.querySelector('input[name="location"]').value = 'Office';
        
        // Show success message
        alert('Check-in time recorded: ' + timeString);
    };
    
    // Quick check-out function
    window.quickCheckOut = function() {
        const now = new Date();
        const timeString = now.toTimeString().slice(0, 5);
        
        // Fill form with current time
        document.querySelector('input[name="check_out"]').value = timeString;
        
        // Show success message
        alert('Check-out time recorded: ' + timeString);
    };
    
    // Form validation
    document.getElementById('attendanceForm').addEventListener('submit', function(e) {
        const status = document.querySelector('select[name="status"]').value;
        const checkIn = document.querySelector('input[name="check_in"]').value;
        const checkOut = document.querySelector('input[name="check_out"]').value;
        
        if (status === 'Present' && !checkIn) {
            e.preventDefault();
            alert('Check-in time is required for Present status.');
            return;
        }
        
        if (checkIn && checkOut && checkOut <= checkIn) {
            e.preventDefault();
            alert('Check-out time must be after check-in time.');
            return;
        }
    });
});
</script>
{% endblock %}
