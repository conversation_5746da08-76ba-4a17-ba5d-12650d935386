{% extends 'base.html' %}

{% block title %}Leave & Attendance Dashboard - Smart Management System{% endblock %}

{% block extra_css %}
<style>
    .dashboard-stats {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .stat-item {
        text-align: center;
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .quick-action-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .quick-action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }
    
    .quick-action-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
    }
    
    .leave-balance-card {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .attendance-chart {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .holiday-item {
        background: #f8fafc;
        border-left: 4px solid var(--warning-color);
        padding: 1rem;
        margin-bottom: 0.5rem;
        border-radius: 0 10px 10px 0;
    }
    
    .recent-leave-item {
        background: white;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 0.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        border-left: 4px solid var(--primary-color);
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-pending { background: #fef3c7; color: #92400e; }
    .status-approved { background: #d1fae5; color: #065f46; }
    .status-rejected { background: #fee2e2; color: #991b1b; }
</style>
{% endblock %}

{% block content %}
<!-- Welcome Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white">
                    <h1 class="mb-2">
                        <i class="fas fa-calendar-check me-3"></i>
                        Welcome, {{ employee.full_name }}!
                    </h1>
                    <p class="lead mb-0">Leave & Attendance Management Dashboard</p>
                    <small>Employee ID: {{ employee.employee_code }} | {{ employee.department.name }} - {{ employee.designation.title }}</small>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Stats -->
<div class="container mt-4">
    <div class="dashboard-stats">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ total_leave_balance }}</div>
                    <div class="stat-label">Total Leave Balance</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ present_days }}</div>
                    <div class="stat-label">Present Days ({{ current_month }})</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ total_working_hours|floatformat:1 }}</div>
                    <div class="stat-label">Working Hours ({{ current_month }})</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-number">{{ wfh_days }}</div>
                    <div class="stat-label">WFH Days ({{ current_month }})</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="container">
    <h3 class="mb-4"><i class="fas fa-bolt me-2"></i>Quick Actions</h3>
    <div class="row">
        <div class="col-md-3 mb-3">
            <a href="{% url 'core:leave_request_create' %}" class="text-decoration-none">
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <h5>Apply for Leave</h5>
                    <p class="text-muted mb-0">Submit a new leave request</p>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{% url 'core:mark_attendance' %}" class="text-decoration-none">
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h5>Mark Attendance</h5>
                    <p class="text-muted mb-0">Check-in/Check-out</p>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{% url 'core:leave_request_list' %}" class="text-decoration-none">
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <h5>My Leave Requests</h5>
                    <p class="text-muted mb-0">View leave history</p>
                </div>
            </a>
        </div>
        <div class="col-md-3 mb-3">
            <a href="{% url 'core:attendance_list' %}" class="text-decoration-none">
                <div class="quick-action-card">
                    <div class="quick-action-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h5>My Attendance</h5>
                    <p class="text-muted mb-0">View attendance records</p>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container mt-4">
    <div class="row">
        <!-- Leave Balances -->
        <div class="col-lg-6">
            <h4 class="mb-3"><i class="fas fa-balance-scale me-2"></i>Leave Balances</h4>
            {% for balance in leave_balances %}
            <div class="leave-balance-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">{{ balance.leave_type.name }}</h5>
                        <small>{{ balance.year }}</small>
                    </div>
                    <div class="text-end">
                        <div class="h4 mb-0">{{ balance.remaining_days }}</div>
                        <small>of {{ balance.total_allocated }} days</small>
                    </div>
                </div>
                <div class="progress mt-2" style="height: 6px;">
                    <div class="progress-bar bg-light" role="progressbar" 
                         style="width: {% widthratio balance.remaining_days balance.total_allocated 100 %}%"></div>
                </div>
            </div>
            {% empty %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>No leave balances configured for this year.
            </div>
            {% endfor %}
        </div>
        
        <!-- Recent Leave Requests -->
        <div class="col-lg-6">
            <h4 class="mb-3"><i class="fas fa-history me-2"></i>Recent Leave Requests</h4>
            {% for leave in recent_leaves %}
            <div class="recent-leave-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1">{{ leave.leave_type.name }}</h6>
                        <small class="text-muted">{{ leave.start_date }} to {{ leave.end_date }}</small>
                        <p class="mb-1 mt-1">{{ leave.reason|truncatechars:50 }}</p>
                    </div>
                    <span class="status-badge status-{{ leave.status|lower }}">{{ leave.status }}</span>
                </div>
            </div>
            {% empty %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>No leave requests found.
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Upcoming Holidays -->
    <div class="row mt-4">
        <div class="col-lg-12">
            <h4 class="mb-3"><i class="fas fa-calendar-alt me-2"></i>Upcoming Holidays</h4>
            <div class="row">
                {% for holiday in upcoming_holidays %}
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="holiday-item">
                        <h6 class="mb-1">{{ holiday.name }}</h6>
                        <small class="text-muted">{{ holiday.date|date:"F d, Y" }}</small>
                        {% if holiday.is_optional %}
                        <span class="badge bg-warning ms-2">Optional</span>
                        {% endif %}
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>No upcoming holidays.
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh dashboard every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
</script>
{% endblock %}
