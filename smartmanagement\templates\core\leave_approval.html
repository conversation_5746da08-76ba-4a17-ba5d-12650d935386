{% extends 'base.html' %}

{% block title %}Approve Leave Request - Smart Management System{% endblock %}

{% block extra_css %}
<style>
    .approval-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }
    
    .approval-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .employee-info {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 25px;
        border-left: 4px solid #667eea;
    }
    
    .leave-details {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 25px;
        border-left: 4px solid #28a745;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #2d3436;
        flex: 0 0 40%;
    }
    
    .info-value {
        color: #636e72;
        flex: 1;
        text-align: right;
    }
    
    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-pending {
        background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
        color: #2d3436;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-label {
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 8px;
        display: block;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-approve {
        background: linear-gradient(135deg, #00b894, #55efc4);
        border: none;
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
    }
    
    .btn-approve:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 184, 148, 0.4);
        color: white;
    }
    
    .btn-reject {
        background: linear-gradient(135deg, #e17055, #fd79a8);
        border: none;
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
    }
    
    .btn-reject:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(225, 112, 85, 0.4);
        color: white;
    }
    
    .btn-secondary {
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
    }
    
    .text-danger {
        font-size: 0.875rem;
        margin-top: 5px;
    }
    
    .reason-box {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .reason-text {
        font-style: italic;
        color: #856404;
        margin: 0;
    }
    
    .decision-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin-top: 20px;
    }
    
    .balance-info {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .balance-warning {
        background: #fff3e0;
        border: 1px solid #ffcc02;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        color: #e65100;
    }
</style>
{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<div class="container mt-3">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{% url 'core:admin_dashboard' %}">
                    <i class="fas fa-home me-1"></i>Admin Dashboard
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'core:leave_request_list' %}">Leave Requests</a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'core:leave_request_detail' leave_request.pk %}">Request Details</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">Approve Request</li>
        </ol>
    </nav>
</div>

<!-- Header Section -->
<section class="py-4" style="background: var(--gradient-bg);">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="text-white d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="mb-2">
                            <i class="fas fa-user-check me-3"></i>Approve Leave Request
                        </h1>
                        <p class="lead mb-0">Review and approve or reject the leave request</p>
                    </div>
                    <div>
                        <a href="{% url 'core:leave_request_detail' leave_request.pk %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="approval-container">
                <div class="approval-header">
                    <h2><i class="fas fa-clipboard-check me-2"></i>Leave Request Approval</h2>
                    <p class="mb-0">Review the details below and make your decision</p>
                </div>
                
                <div class="p-4">
                    <!-- Employee Information -->
                    <div class="employee-info">
                        <h5><i class="fas fa-user me-2"></i>Employee Information</h5>
                        <div class="info-row">
                            <span class="info-label">Employee Name:</span>
                            <span class="info-value">{{ employee.full_name }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Employee Code:</span>
                            <span class="info-value">{{ employee.employee_code }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Department:</span>
                            <span class="info-value">{{ employee.department.name }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Designation:</span>
                            <span class="info-value">{{ employee.designation.name }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Email:</span>
                            <span class="info-value">{{ employee.user.email }}</span>
                        </div>
                    </div>
                    
                    <!-- Leave Request Details -->
                    <div class="leave-details">
                        <h5><i class="fas fa-calendar-alt me-2"></i>Leave Request Details</h5>
                        <div class="info-row">
                            <span class="info-label">Leave Type:</span>
                            <span class="info-value">{{ leave_request.leave_type.name }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Start Date:</span>
                            <span class="info-value">{{ leave_request.start_date|date:"F d, Y" }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">End Date:</span>
                            <span class="info-value">{{ leave_request.end_date|date:"F d, Y" }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Total Days:</span>
                            <span class="info-value">{{ leave_request.total_days }} day{{ leave_request.total_days|floatformat:0|pluralize }}</span>
                        </div>
                        {% if leave_request.is_half_day %}
                        <div class="info-row">
                            <span class="info-label">Half Day Period:</span>
                            <span class="info-value">{{ leave_request.half_day_period }}</span>
                        </div>
                        {% endif %}
                        <div class="info-row">
                            <span class="info-label">Applied On:</span>
                            <span class="info-value">{{ leave_request.applied_on|date:"F d, Y" }} at {{ leave_request.applied_on|time:"g:i A" }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">Current Status:</span>
                            <span class="info-value">
                                <span class="status-badge status-{{ leave_request.status|lower }}">
                                    {{ leave_request.status }}
                                </span>
                            </span>
                        </div>
                        {% if leave_request.emergency_contact %}
                        <div class="info-row">
                            <span class="info-label">Emergency Contact:</span>
                            <span class="info-value">{{ leave_request.emergency_contact }}</span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Leave Reason -->
                    <div class="reason-box">
                        <h6><i class="fas fa-comment me-2"></i>Reason for Leave</h6>
                        <p class="reason-text">{{ leave_request.reason }}</p>
                    </div>
                    
                    <!-- Leave Balance Information -->
                    {% if balance_info.has_sufficient_balance %}
                    <div class="balance-info">
                        <h6><i class="fas fa-chart-bar me-2"></i>Leave Balance Information</h6>
                        <p class="mb-1"><strong>Leave Type:</strong> {{ leave_request.leave_type.name }}</p>
                        <p class="mb-1"><strong>Max Days Per Year:</strong> {{ leave_request.leave_type.max_days_per_year }} days</p>
                        <p class="mb-1"><strong>Available Days:</strong> {{ balance_info.available_days }} days</p>
                        <p class="mb-1"><strong>Requested Days:</strong> {{ balance_info.requested_days }} days</p>
                        <p class="mb-0"><strong>Balance After Approval:</strong> {{ balance_info.balance_after_approval }} days</p>
                    </div>
                    {% else %}
                    <div class="balance-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Insufficient Leave Balance</h6>
                        <p class="mb-1"><strong>Available Days:</strong> {{ balance_info.available_days }} days</p>
                        <p class="mb-1"><strong>Requested Days:</strong> {{ balance_info.requested_days }} days</p>
                        <p class="mb-0"><strong>Shortage:</strong> {{ balance_info.shortage }} days</p>
                        <small class="text-muted">Consider rejecting this request or discussing with the employee.</small>
                    </div>
                    {% endif %}
                    
                    <!-- Approval Form -->
                    <div class="decision-section">
                        <h5><i class="fas fa-gavel me-2"></i>Make Your Decision</h5>
                        
                        <form method="post" id="approvalForm">
                            {% csrf_token %}
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="{{ form.status.id_for_label }}" class="form-label">
                                            Decision <span class="text-danger">*</span>
                                        </label>
                                        {{ form.status }}
                                        {% if form.status.errors %}
                                            <div class="text-danger mt-1">{{ form.status.errors.0 }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="{{ form.rejection_reason.id_for_label }}" class="form-label">
                                    Comments / Rejection Reason
                                </label>
                                {{ form.rejection_reason }}
                                {% if form.rejection_reason.errors %}
                                    <div class="text-danger mt-1">{{ form.rejection_reason.errors.0 }}</div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    Provide comments for approval or reason for rejection (required if rejecting)
                                </small>
                            </div>
                            
                            <!-- Form Actions -->
                            <div class="d-flex justify-content-between align-items-center mt-4">
                                <a href="{% url 'core:leave_request_detail' leave_request.pk %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                                <div>
                                    <button type="submit" name="decision" value="Approved" class="btn btn-approve me-2">
                                        <i class="fas fa-check me-2"></i>Approve Request
                                    </button>
                                    <button type="submit" name="decision" value="Rejected" class="btn btn-reject">
                                        <i class="fas fa-times me-2"></i>Reject Request
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('{{ form.status.id_for_label }}');
    const rejectionReasonField = document.getElementById('{{ form.rejection_reason.id_for_label }}');
    const approveBtn = document.querySelector('button[value="Approved"]');
    const rejectBtn = document.querySelector('button[value="Rejected"]');
    
    // Handle button clicks to set the status
    if (approveBtn) {
        approveBtn.addEventListener('click', function(e) {
            statusSelect.value = 'Approved';
        });
    }
    
    if (rejectBtn) {
        rejectBtn.addEventListener('click', function(e) {
            statusSelect.value = 'Rejected';
            
            // Validate rejection reason
            if (!rejectionReasonField.value.trim()) {
                e.preventDefault();
                alert('Please provide a reason for rejection.');
                rejectionReasonField.focus();
                return false;
            }
        });
    }
    
    // Form validation
    document.getElementById('approvalForm').addEventListener('submit', function(e) {
        const status = statusSelect.value;
        const rejectionReason = rejectionReasonField.value.trim();
        
        if (!status) {
            e.preventDefault();
            alert('Please select a decision (Approve or Reject).');
            return false;
        }
        
        if (status === 'Rejected' && !rejectionReason) {
            e.preventDefault();
            alert('Please provide a reason for rejection.');
            rejectionReasonField.focus();
            return false;
        }
        
        // Confirmation dialog
        const action = status === 'Approved' ? 'approve' : 'reject';
        const confirmMessage = `Are you sure you want to ${action} this leave request for {{ employee.full_name }}?`;
        
        if (!confirm(confirmMessage)) {
            e.preventDefault();
            return false;
        }
    });
});
</script>
{% endblock %}
